import { getBackendUrl } from '$src/lib/config';
import { ApiError } from '../../client/errors';
import type {
    SubscriptionQuotaStatusResponse,
    SubscriptionInfoResponse,
    SubscriptionQuotaCheckResponse,
    SubscriptionFeatureCheckResponse,
    LineQuotaCheckResponse
} from '../../types/subscription-api';

export class SubscriptionService {
    private baseUrl = `${getBackendUrl()}/subscription/api/subscription`;

    /**
     * Get current quota status including usage and limits
     */
    async getQuotaStatus(token: string): Promise<SubscriptionQuotaStatusResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/status/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error fetching quota status:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch quota status'
            };
        }
    }

    /**
     * Check if a new user can be created (quota validation)
     */
    async checkUserCreation(token: string): Promise<SubscriptionQuotaCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/user-creation-check/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking user creation quota:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check user creation quota'
            };
        }
    }

    /**
     * Check if a new LINE account can be created (quota validation)
     */
    async checkLINEAccountCreation(token: string): Promise<LineQuotaCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/quota/line-account-creation-check/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking LINE account creation quota:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check LINE account creation quota'
            };
        }
    }

    /**
     * Check if a specific feature is enabled
     */
    async checkFeatureAccess(feature: string, token: string): Promise<SubscriptionFeatureCheckResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/feature/check/?feature=${encodeURIComponent(feature)}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error checking feature access:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to check feature access'
            };
        }
    }

    /**
     * Get subscription information (filtered based on user permissions)
     */
    async getSubscriptionInfo(token: string): Promise<SubscriptionInfoResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/info/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const responseData = await response.json();
            return {
                data: responseData.data,
                message: responseData.message,
                res_status: response.status
            };

        } catch (error) {
            console.error('Error fetching subscription info:', error);
            return {
                data: null,
                res_status: error.status || 500,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch subscription info'
            };
        }
    }

    /**
     * Get available subscription tiers (for comparison)
     * This would typically come from a separate endpoint or be hardcoded
     */
    async getAvailableTiers(token: string) {
        // For now, return static tier data since this isn't specified in the backend API
        // In a real implementation, this might come from a separate endpoint
        return {
            tiers: [
                {
                    id: "premium",
                    name: "Premium",
                    price: "$99",
                    period: "/month",
                    description: "Perfect for small teams getting started",
                    icon: "Shield",
                    popular: false,
                    color: "blue",
                    quota: {
                        max_active_users: 5,
                        max_line_accounts: 5,
                        max_ai_workflow_units: 1,
                        max_messages_per_min: 30,
                        max_storage_gb: 250
                    },
                    features: {
                        custom_transfer_algo: false,
                        custom_case_desc: false,
                        custom_ai_workflow: false,
                        ai_quick_reply: true,
                        ai_smart_reply: false,
                        ai_memory: false,
                        crm_integration: false,
                        crm_notify_claim: false,
                        crm_case_system: false,
                        dashboard_sla_config: false,
                        dashboard_sla_alert: false,
                        broadcasting: true
                    },
                    highlights: ["5 Active User Accounts", "250 GB Storage", "Basic AI Assistant", "Working Hours Support"]
                },
                {
                    id: "enterprise",
                    name: "Enterprise",
                    price: "$299",
                    period: "/month",
                    description: "Advanced features for growing businesses",
                    icon: "Zap",
                    popular: true,
                    color: "purple",
                    quota: {
                        max_active_users: 20,
                        max_line_accounts: 10,
                        max_ai_workflow_units: 5,
                        max_messages_per_min: 100,
                        max_storage_gb: 1000
                    },
                    features: {
                        custom_transfer_algo: true,
                        custom_case_desc: true,
                        custom_ai_workflow: true,
                        ai_quick_reply: true,
                        ai_smart_reply: true,
                        ai_memory: true,
                        crm_integration: true,
                        crm_notify_claim: false,
                        crm_case_system: false,
                        dashboard_sla_config: true,
                        dashboard_sla_alert: false,
                        broadcasting: true
                    },
                    highlights: [
                        "20 Active User Accounts",
                        "1 TB Storage",
                        "Advanced AI with Memory",
                        "24/7 Support",
                        "Custom Workflows"
                    ]
                },
                {
                    id: "enterprise_plus",
                    name: "Enterprise Plus",
                    price: "$599",
                    period: "/month",
                    description: "Complete solution for enterprise organizations",
                    icon: "Crown",
                    popular: false,
                    color: "gold",
                    quota: {
                        max_active_users: 50,
                        max_line_accounts: "unlimited",
                        max_ai_workflow_units: "unlimited",
                        max_messages_per_min: 200,
                        max_storage_gb: 2000
                    },
                    features: {
                        custom_transfer_algo: true,
                        custom_case_desc: true,
                        custom_ai_workflow: true,
                        ai_quick_reply: true,
                        ai_smart_reply: true,
                        ai_memory: true,
                        crm_integration: true,
                        crm_notify_claim: true,
                        crm_case_system: true,
                        dashboard_sla_config: true,
                        dashboard_sla_alert: true,
                        broadcasting: true
                    },
                    highlights: [
                        "50 Active User Accounts",
                        "2 TB Storage",
                        "Unlimited AI Workflows",
                        "Complete Case System",
                        "SLA Monitoring"
                    ]
                }
            ],
            res_status: 200
        };
    }
}
