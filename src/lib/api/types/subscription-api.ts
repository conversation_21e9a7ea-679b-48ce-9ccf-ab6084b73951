// Backend API response types based on subscription-backend.md

export interface SubscriptionQuotaData {
    organization_name: string;
    tier_name: string;
    status: string;
    expires_at: string;
    quota: {
        max_active_users: number;
        current_active_users: number;
        remaining_slots: number;
        max_line_accounts: number | string; // can be "unlimited"
        current_line_accounts: number;
        max_ai_workflow_units: number | string; // can be "unlimited"
        current_ai_workflow_units: number;
        max_messages_per_min: number;
        max_storage_gb: number;
    };
    features: {
        custom_transfer_algo: boolean;
        custom_case_desc: boolean;
        custom_ai_workflow: boolean;
        ai_quick_reply: boolean;
        ai_smart_reply: boolean;
        ai_memory: boolean;
        crm_integration: boolean;
        crm_notify_claim: boolean;
        crm_case_system: boolean;
        dashboard_sla_config: boolean;
        dashboard_sla_alert: boolean;
        broadcasting: boolean;
    };
}

export interface SubscriptionQuotaStatusResponse {
    data: SubscriptionQuotaData | null;
    message?: string;
    res_status: number;
    error_msg?: string;
}

export interface SubscriptionQuotaCheckData {
    allowed: boolean;
    quota_info: {
        current_users: number;
        max_users: number;
        remaining_slots: number;
        tier_name: string;
        organization_name: string;
    };
}

export interface SubscriptionQuotaCheckResponse {
    data: SubscriptionQuotaCheckData | null;
    message?: string;
    res_status: number;
    error_msg?: string;
}

export interface LineQuotaCheckData {
    allowed: boolean;
    quota_info: {
        current_line_accounts: number;
        max_line_accounts: number | string;
        remaining_slots: number;
    };
}

export interface LineQuotaCheckResponse {
    data: LineQuotaCheckData | null;
    message?: string;
    res_status: number;
    error_msg?: string;
}

export interface SubscriptionFeatureCheckData {
    feature: string;
    has_access: boolean;
}

export interface SubscriptionFeatureCheckResponse {
    data: SubscriptionFeatureCheckData | null;
    message?: string;
    res_status: number;
    error_msg?: string;
}

export interface SubscriptionInfoData {
    organization_name: string;
    subscription_key: string;
    tier_id: string;
    tier_name: string;
    status: string;
    activated_on: string;
    expires_at: string;
    quota: {
        max_active_users: number;
        max_line_accounts: number | string;
        max_ai_workflow_units: number | string;
        max_messages_per_min: number;
        max_storage_gb: number;
    };
    features: {
        custom_transfer_algo: boolean;
        custom_case_desc: boolean;
        custom_ai_workflow: boolean;
        ai_quick_reply: boolean;
        ai_smart_reply: boolean;
        ai_memory: boolean;
        crm_integration: boolean;
        crm_notify_claim: boolean;
        crm_case_system: boolean;
        dashboard_sla_config: boolean;
        dashboard_sla_alert: boolean;
        broadcasting: boolean;
    };
    metadata?: {
        billing_contact?: string;
        technical_contact?: string;
    };
}

export interface SubscriptionInfoResponse {
    data: SubscriptionInfoData | null;
    message?: string;
    res_status: number;
    error_msg?: string;
}

// Additional types for tier comparison
export interface SubscriptionTierData {
    id: string;
    name: string;
    price: string;
    period: string;
    description: string;
    icon: string;
    popular: boolean;
    color: string;
    quota: {
        max_active_users: number;
        max_line_accounts: number | string;
        max_ai_workflow_units: number | string;
        max_messages_per_min: number;
        max_storage_gb: number;
    };
    features: {
        custom_transfer_algo: boolean;
        custom_case_desc: boolean;
        custom_ai_workflow: boolean;
        ai_quick_reply: boolean;
        ai_smart_reply: boolean;
        ai_memory: boolean;
        crm_integration: boolean;
        crm_notify_claim: boolean;
        crm_case_system: boolean;
        dashboard_sla_config: boolean;
        dashboard_sla_alert: boolean;
        broadcasting: boolean;
    };
    highlights: string[];
}

export interface SubscriptionTiersResponse {
    tiers: SubscriptionTierData[];
    res_status: number;
    error_msg?: string;
}
